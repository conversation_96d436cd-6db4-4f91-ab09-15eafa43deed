// src/schedule/schedule.service.ts
import {
  BadRequestException,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { Doctor } from 'src/doctor/entities/doctor.entity';
import { WeeklySchedule, DayOfWeek } from './entities/weeklySchedule.entity';
import { WeeklySession } from './entities/weeklySession.entity';
import { OverrideSchedule } from './entities/overrideSchedule.entity';
import { OverrideSession } from './entities/overrideSession.entity';
import { CreateWeeklyScheduleDto } from './dtos/create-weekly-schedule.dto';
import { CreateMultipleWeeklyScheduleDto } from './dtos/create-multiple-weekly-schedule.dto';
import { CreateOverrideScheduleDto } from './dtos/create-override-schedule.dto';
import { CreateMultipleOverrideScheduleDto } from './dtos/create-multiple-override-schedule.dto';
import { UpdateWeeklyScheduleDto } from './dtos/update-weekly-schedule.dto';
import { UpdateOverrideScheduleDto } from './dtos/update-override-schedule.dto';
import { UserType } from 'src/common/enums/userType.enum';
import { GetSlotDetailsDto } from './dtos/get-slot-details.dto';
import * as moment from 'moment';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { DoctorService } from 'src/doctor/doctor.service';
import { AppointmentService } from 'src/appointment/appointment.service';

@Injectable()
export class ScheduleService {
  // CONSTRUCTOR
  constructor(
    @Inject(forwardRef(() => DoctorService))
    private readonly doctorService: DoctorService,

    @InjectRepository(WeeklySchedule)
    private readonly weeklyScheduleRepository: Repository<WeeklySchedule>,

    @InjectRepository(WeeklySession)
    private readonly weeklySessionRepository: Repository<WeeklySession>,

    @InjectRepository(OverrideSchedule)
    private readonly overrideScheduleRepository: Repository<OverrideSchedule>,

    @InjectRepository(OverrideSession)
    private readonly overrideSessionRepository: Repository<OverrideSession>,

    @Inject(forwardRef(() => AppointmentService))
    private readonly appointmentService: AppointmentService,
  ) {}

  // SERVICES
  async createWeeklyScheduleForSingleDay(
    user: Doctor,
    createWeeklyScheduleDto: CreateWeeklyScheduleDto,
  ): Promise<WeeklySchedule[]> {
    const { doctorId, dayOfWeek, sessions } = createWeeklyScheduleDto;

    // SELF CHECK
    const doctorIdToUse = doctorId ? doctorId : user.id;
    if (doctorId && doctorId !== user.id) {
      throw new BadRequestException(
        'You cannot create a weekly schedule for other doctors',
      );
    }

    // DOCTOR CHECK
    const doctor = await this.doctorService.findById(doctorIdToUse);

    if (!doctor) {
      throw new BadRequestException('No doctor found for this id');
    }

    // CHECK IF WEEKLY SCHEDULE FOR THIS DAY ALREADY EXISTS
    const existingSchedule = await this.weeklyScheduleRepository.findOne({
      where: { doctor: { id: doctor.id }, dayOfWeek },
    });
    if (existingSchedule) {
      throw new BadRequestException(
        `Weekly schedule already exists for ${dayOfWeek}`,
      );
    }

    // CHECK IF SLOT DURATION IS ACCORDING TO START AND END TIME
    sessions.forEach((session, index) => {
      const start = moment(session.startTime, 'HH:mm');
      const end = moment(session.endTime, 'HH:mm');

      if (end.isBefore(start)) {
        throw new BadRequestException(
          `Session ${index + 1}: Start-Time cant be earlier than End-Time`,
        );
      }

      if (!start.isValid() || !end.isValid()) {
        throw new BadRequestException(
          `Session ${index + 1}: Invalid start or end time format.`,
        );
      }

      const totalDuration = end.diff(start, 'minutes');

      if (totalDuration < session.slotDuration) {
        throw new BadRequestException(
          `Session ${index + 1}: Slot duration (${session.slotDuration} mins) exceeds session duration (${totalDuration} mins).`,
        );
      }
    });

    // CHECK FOR OVERLAPPING SESSIONS
    const sortedSessions = sessions.slice().sort((a, b) => {
      return moment(a.startTime, 'HH:mm').diff(moment(b.startTime, 'HH:mm'));
    });

    for (let i = 0; i < sortedSessions.length - 1; i++) {
      const currentEnd = moment(sortedSessions[i].endTime, 'HH:mm');
      const nextStart = moment(sortedSessions[i + 1].startTime, 'HH:mm');

      if (currentEnd.isAfter(nextStart)) {
        throw new BadRequestException(
          `Session ${i + 1} overlaps with session ${i + 2}.`,
        );
      }
    }

    // CREATE WEEKLY SCHEDULE
    const weeklySchedule = this.weeklyScheduleRepository.create({
      doctor,
      dayOfWeek,
    });

    const savedSchedule =
      await this.weeklyScheduleRepository.save(weeklySchedule);

    // CREATE SESSIONS
    const sessionEntities = sessions.map((session) => {
      return this.weeklySessionRepository.create({
        weeklySchedule: savedSchedule,
        startTime: session.startTime,
        endTime: session.endTime,
        slotDuration: session.slotDuration,
      });
    });
    await this.weeklySessionRepository.save(sessionEntities);

    // RETURN WEEKLY SCHEDULE AND SESSIONS
    return this.weeklyScheduleRepository.find({
      where: { doctor: { id: doctor.id } },
      relations: ['sessions'],
    });
  }
  async createMultipleWeeklySchedules(
    user: Doctor,
    createMultipleWeeklyScheduleDto: CreateMultipleWeeklyScheduleDto,
  ): Promise<WeeklySchedule[]> {
    const { doctorId, schedules } = createMultipleWeeklyScheduleDto;
    const doctorIdToUse = doctorId ? doctorId : user.id;

    // SELF CHECK
    if (doctorId && doctorId !== user.id) {
      throw new BadRequestException(
        'You cannot create weekly schedules for other doctors',
      );
    }

    // DUPLICATE DAYS IN PAYLOAD CHECK
    const daySet = new Set<string>();
    for (const schedule of schedules) {
      if (daySet.has(schedule.dayOfWeek)) {
        throw new BadRequestException(
          `Duplicate weekly schedule found for day: ${schedule.dayOfWeek.toUpperCase()}`,
        );
      }
      daySet.add(schedule.dayOfWeek);
    }

    // DOCTOR CHECK
    const doctor = await this.doctorService.findById(doctorIdToUse);

    if (!doctor) {
      throw new BadRequestException('No doctor found for this id');
    }

    // DUPLICATION CHECK

    // CHECK IF SLOT DURATION IS ACCORDING TO START AND END TIME
    for (const sched of schedules) {
      sched.sessions.forEach((session, index) => {
        const start = moment(session.startTime, 'HH:mm');
        const end = moment(session.endTime, 'HH:mm');

        if (!start.isValid() || !end.isValid()) {
          throw new BadRequestException(
            `${sched.dayOfWeek.toUpperCase()}: Session ${index + 1}: Invalid start or end time format.`,
          );
        }

        const totalDuration = end.diff(start, 'minutes');

        if (totalDuration <= 0) {
          throw new BadRequestException(
            `${sched.dayOfWeek.toUpperCase()}: Session ${index + 1}: start-time should be earlier than end-time`,
          );
        }

        if (totalDuration < session.slotDuration) {
          throw new BadRequestException(
            `${sched.dayOfWeek.toUpperCase()}: Session ${index + 1}: Slot duration (${session.slotDuration} mins) exceeds session duration (${totalDuration} mins).`,
          );
        }
      });
    }

    // CHECK FOR OVERLAPPING SESSIONS
    for (const sched of schedules) {
      const sortedSessions = sched.sessions.slice().sort((a, b) => {
        return moment(a.startTime, 'HH:mm').diff(moment(b.startTime, 'HH:mm'));
      });

      for (let i = 0; i < sortedSessions.length - 1; i++) {
        const currentEnd = moment(sortedSessions[i].endTime, 'HH:mm');
        const nextStart = moment(sortedSessions[i + 1].startTime, 'HH:mm');

        if (currentEnd.isAfter(nextStart)) {
          throw new BadRequestException(
            `${sched.dayOfWeek.toUpperCase()}: Session ${i + 1} overlaps with session ${i + 2}.`,
          );
        }
      }
    }

    // CREATE WEEKLY SCHEDULE
    const createdSchedules: WeeklySchedule[] = [];
    for (const sched of schedules) {
      const existingSchedule = await this.weeklyScheduleRepository.findOne({
        where: { doctor: { id: doctor.id }, dayOfWeek: sched.dayOfWeek },
      });
      if (existingSchedule) {
        // CLEAR OLD SCHEDULES

        await this.weeklySessionRepository.delete({
          weeklySchedule: { id: existingSchedule.id },
        });

        await this.weeklyScheduleRepository.delete({
          dayOfWeek: existingSchedule.dayOfWeek,
        });
      }
      const newWeeklySchedule = this.weeklyScheduleRepository.create({
        doctor,
        dayOfWeek: sched.dayOfWeek,
        timezone: sched.timezone,
      });
      const saved = await this.weeklyScheduleRepository.save(newWeeklySchedule);

      // CREATE SESSION
      const sessionEntities = sched.sessions.map((session) =>
        this.weeklySessionRepository.create({
          weeklySchedule: saved,
          startTime: session.startTime,
          endTime: session.endTime,
          slotDuration: session.slotDuration,
        }),
      );
      await this.weeklySessionRepository.save(sessionEntities);

      createdSchedules.push(saved);
    }

    // RETURN ALL WEEKLY SCHEDULES (WITH SESSIONS)
    return this.weeklyScheduleRepository.find({
      where: { doctor: { id: doctor.id } },
      relations: ['sessions'],
    });
  }
  async createOverrideSchedule(
    user: Doctor,
    createOverrideScheduleDto: CreateOverrideScheduleDto,
  ): Promise<OverrideSchedule[]> {
    const { doctorId, date, sessions, timezone } = createOverrideScheduleDto;

    // SELF CHECK
    const doctorIdToUse = doctorId ? doctorId : user.id;
    if (doctorId && doctorId !== user.id) {
      throw new BadRequestException(
        'You cannot create an override schedule for other doctors',
      );
    }

    // DOCTOR CHECK
    const doctor = await this.doctorService.findById(doctorIdToUse);

    if (!doctor) {
      throw new BadRequestException('No doctor found for this id');
    }

    // DUPLICATION CHECK
    const existingOverride = await this.overrideScheduleRepository.findOne({
      where: { doctor: { id: doctor.id }, date },
    });
    if (existingOverride) {
      throw new BadRequestException(
        `Override schedule already exists for date ${date}`,
      );
    }

    // CHECK IF SLOT DURATION IS ACCORDING TO START AND END TIME
    sessions.forEach((session, index) => {
      const start = moment(session.startTime, 'HH:mm');
      const end = moment(session.endTime, 'HH:mm');

      if (!start.isValid() || !end.isValid()) {
        throw new BadRequestException(
          `Session ${index + 1}: Invalid start or end time format.`,
        );
      }

      const totalDuration = end.diff(start, 'minutes');

      if (totalDuration < session.slotDuration) {
        throw new BadRequestException(
          `Session ${index + 1}: Slot duration (${session.slotDuration} mins) exceeds session duration (${totalDuration} mins).`,
        );
      }
    });

    // CHECK FOR OVERLAPPING SESSIONS
    const sortedSessions = sessions.slice().sort((a, b) => {
      return moment(a.startTime, 'HH:mm').diff(moment(b.startTime, 'HH:mm'));
    });

    for (let i = 0; i < sortedSessions.length - 1; i++) {
      const currentEnd = moment(sortedSessions[i].endTime, 'HH:mm');
      const nextStart = moment(sortedSessions[i + 1].startTime, 'HH:mm');

      if (currentEnd.isAfter(nextStart)) {
        throw new BadRequestException(
          `Session ${i + 1} overlaps with session ${i + 2}.`,
        );
      }
    }

    // CREATE OVERRIDE SCHEDULE
    const overrideSchedule = this.overrideScheduleRepository.create({
      doctor,
      date,
      timezone,
    });
    const savedOverride =
      await this.overrideScheduleRepository.save(overrideSchedule);

    // CREATE SESSIONS
    const sessionEntities = sessions.map((session) =>
      this.overrideSessionRepository.create({
        overrideSchedule: savedOverride,
        startTime: session.startTime,
        endTime: session.endTime,
        slotDuration: session.slotDuration,
      }),
    );
    await this.overrideSessionRepository.save(sessionEntities);

    // RETURN ALL OVERRIDE SCHDULES WITH SESSIONS
    return this.overrideScheduleRepository.find({
      where: { doctor: { id: doctor.id } },
      relations: ['sessions'],
    });
  }
  async createMultipleOverrideSchedules(
    user: Doctor,
    createMultipleOverrideScheduleDto: CreateMultipleOverrideScheduleDto,
  ): Promise<OverrideSchedule[]> {
    const { doctorId, overrideSchedules } = createMultipleOverrideScheduleDto;
    const doctorIdToUse = doctorId ? doctorId : user.id;

    // SELF CHECK
    if (doctorId && doctorId !== user.id) {
      throw new BadRequestException(
        'You cannot create override schedules for other doctors',
      );
    }

    // DUPLICATE DATES IN PAYLOAD CHECK
    const dateSet = new Set<string>();
    for (const schedule of overrideSchedules) {
      const dateStr = moment(schedule.date).format('YYYY-MM-DD');
      if (dateSet.has(dateStr)) {
        throw new BadRequestException(
          `Duplicate override schedule found for date ${dateStr}`,
        );
      }
      dateSet.add(dateStr);
    }

    // DOCTOR CHECK
    const doctor = await this.doctorService.findById(doctorIdToUse);

    if (!doctor) {
      throw new BadRequestException('No doctor found for this id');
    }

    // CHECK IF SLOT DURATION IS ACCORDING TO START AND END TIME
    for (const ovr of overrideSchedules) {
      ovr.sessions.forEach((session, index) => {
        const start = moment(session.startTime, 'HH:mm');
        const end = moment(session.endTime, 'HH:mm');

        if (!start.isValid() || !end.isValid()) {
          throw new BadRequestException(
            `Session ${index + 1}: Invalid start or end time format.`,
          );
        }

        const totalDuration = end.diff(start, 'minutes');

        if (totalDuration < session.slotDuration) {
          throw new BadRequestException(
            `Session ${index + 1}: Slot duration (${session.slotDuration} mins) exceeds session duration (${totalDuration} mins).`,
          );
        }
      });
    }

    // CHECK FOR OVERLAPPING SESSIONS
    for (const ovr of overrideSchedules) {
      const sortedSessions = ovr.sessions.slice().sort((a, b) => {
        return moment(a.startTime, 'HH:mm').diff(moment(b.startTime, 'HH:mm'));
      });

      for (let i = 0; i < sortedSessions.length - 1; i++) {
        const currentEnd = moment(sortedSessions[i].endTime, 'HH:mm');
        const nextStart = moment(sortedSessions[i + 1].startTime, 'HH:mm');

        if (currentEnd.isAfter(nextStart)) {
          throw new BadRequestException(
            `Session ${i + 1} overlaps with session ${i + 2}.`,
          );
        }
      }
    }

    // CLEAR OLD SCHEDULES
    await this.overrideScheduleRepository.delete({
      doctor: { id: doctorIdToUse },
    });

    // CREATE OVERRIDE SCHEDULES
    const createdOverrideSchedules: OverrideSchedule[] = [];
    for (const ovr of overrideSchedules) {
      const newOverride = this.overrideScheduleRepository.create({
        doctor,
        date: ovr.date,
        timezone: ovr.timezone,
      });
      const saved = await this.overrideScheduleRepository.save(newOverride);

      // CREATE SESSIONS
      const sessionEntities = ovr.sessions.map((session) =>
        this.overrideSessionRepository.create({
          overrideSchedule: saved,
          startTime: session.startTime,
          endTime: session.endTime,
          slotDuration: session.slotDuration,
        }),
      );
      await this.overrideSessionRepository.save(sessionEntities);

      createdOverrideSchedules.push(saved);
    }

    // RETURN OVERRIDE SHCEDULES ALONG WITH SESSIONS
    return this.overrideScheduleRepository.find({
      where: { doctor: { id: doctor.id } },
      relations: ['sessions'],
    });
  }
  async updateWeeklySchedulesDoctor(
    user: Doctor,
    updateWeeklyScheduleDto: UpdateWeeklyScheduleDto,
  ): Promise<WeeklySchedule[]> {
    const { doctorId, schedules } = updateWeeklyScheduleDto;
    const doctorIdToUse = doctorId ? doctorId : user.id;

    // SELF CHECK
    if (doctorId && doctorId !== user.id) {
      throw new BadRequestException(
        'You cannot update weekly schedules for other doctors',
      );
    }

    // DUPLICATE DAYS IN PAYLOAD CHECK
    const daySet = new Set<string>();
    for (const schedule of schedules) {
      if (daySet.has(schedule.dayOfWeek)) {
        throw new BadRequestException(
          `Duplicate weekly schedule found for day ${schedule.dayOfWeek}`,
        );
      }
      daySet.add(schedule.dayOfWeek);
    }

    // DOCTOR CHECK
    const doctor = await this.doctorService.findById(doctorIdToUse);

    if (!doctor) {
      throw new BadRequestException('No doctor found for this id');
    }

    // EDIT ONLY EXISITING DAYS
    for (const sched of schedules) {
      const existingSchedule = await this.weeklyScheduleRepository.findOne({
        where: { doctor: { id: doctor.id }, dayOfWeek: sched.dayOfWeek },
      });
      if (!existingSchedule) {
        throw new BadRequestException(
          `Weekly schedule does not exist for day: ${sched.dayOfWeek}`,
        );
      }
    }

    // CHECK IF SLOT DURATION IS ACCORDING TO START AND END TIME
    for (const sched of schedules) {
      sched.sessions.forEach((session, index) => {
        const start = moment(session.startTime, 'HH:mm');
        const end = moment(session.endTime, 'HH:mm');

        if (!start.isValid() || !end.isValid()) {
          throw new BadRequestException(
            `Session ${index + 1}: Invalid start or end time format.`,
          );
        }

        const totalDuration = end.diff(start, 'minutes');

        if (totalDuration < session.slotDuration) {
          throw new BadRequestException(
            `Session ${index + 1}: Slot duration (${session.slotDuration} mins) exceeds session duration (${totalDuration} mins).`,
          );
        }
      });
    }

    // CHECK FOR OVERLAPPING SESSIONS
    for (const sched of schedules) {
      const sortedSessions = sched.sessions.slice().sort((a, b) => {
        return moment(a.startTime, 'HH:mm').diff(moment(b.startTime, 'HH:mm'));
      });

      for (let i = 0; i < sortedSessions.length - 1; i++) {
        const currentEnd = moment(sortedSessions[i].endTime, 'HH:mm');
        const nextStart = moment(sortedSessions[i + 1].startTime, 'HH:mm');

        if (currentEnd.isAfter(nextStart)) {
          throw new BadRequestException(
            `Session ${i + 1} overlaps with session ${i + 2}.`,
          );
        }
      }
    }

    // UPDATE SCHEDULES
    for (const sched of schedules) {
      const existingSchedule = await this.weeklyScheduleRepository.findOne({
        where: { doctor: { id: doctor.id }, dayOfWeek: sched.dayOfWeek },
      });

      // UPDATE TIMEZONE IF PROVIDED
      if (sched.timezone) {
        existingSchedule.timezone = sched.timezone;
        await this.weeklyScheduleRepository.save(existingSchedule);
      }

      // CLEAR OLD SCHEDULES
      await this.weeklySessionRepository.delete({
        weeklySchedule: { id: existingSchedule.id },
      });

      // CREATE NEW SESSIONS
      const newSessions = sched.sessions.map((session) =>
        this.weeklySessionRepository.create({
          weeklySchedule: existingSchedule,
          startTime: session.startTime,
          endTime: session.endTime,
          slotDuration: session.slotDuration,
        }),
      );
      await this.weeklySessionRepository.save(newSessions);
    }

    // RETURN ALL WEEKLY SCHEDULES
    return this.weeklyScheduleRepository.find({
      where: { doctor: { id: doctor.id } },
      relations: ['sessions'],
    });
  }
  async updateOverrideSchedulesDoctor(
    user: Doctor,
    updateOverrideDto: UpdateOverrideScheduleDto,
  ): Promise<OverrideSchedule[]> {
    const { doctorId, overrideSchedules } = updateOverrideDto;
    const doctorIdToUse = doctorId ? doctorId : user.id;

    // SELF CHECK
    if (doctorId && doctorId !== user.id) {
      throw new BadRequestException(
        'You cannot update override schedules for other doctors',
      );
    }

    // DUPLICATE DATES IN PAYLOAD CHECK
    const dateSet = new Set<string>();
    for (const schedule of overrideSchedules) {
      const dateStr = moment(schedule.date).format('YYYY-MM-DD');
      if (dateSet.has(dateStr)) {
        throw new BadRequestException(
          `Duplicate override schedule found for date ${dateStr}`,
        );
      }
      dateSet.add(dateStr);
    }

    // DOCTOR CHECK
    const doctor = await this.doctorService.findById(doctorIdToUse);

    if (!doctor) {
      throw new BadRequestException('No doctor found for this id');
    }

    // EDIT ONLY EXISTING DATES
    for (const ovr of overrideSchedules) {
      const existingOverride = await this.overrideScheduleRepository.findOne({
        where: { doctor: { id: doctor.id }, date: ovr.date },
      });
      if (!existingOverride) {
        throw new BadRequestException(
          `Override schedule does not exist for date: ${ovr.date}`,
        );
      }
    }

    // CHECK IF SLOT DURATION IS ACCORDING TO START AND END TIME
    for (const ovr of overrideSchedules) {
      ovr.sessions.forEach((session, index) => {
        const start = moment(session.startTime, 'HH:mm');
        const end = moment(session.endTime, 'HH:mm');

        if (!start.isValid() || !end.isValid()) {
          throw new BadRequestException(
            `Session ${index + 1}: Invalid start or end time format.`,
          );
        }

        const totalDuration = end.diff(start, 'minutes');

        if (totalDuration < session.slotDuration) {
          throw new BadRequestException(
            `Session ${index + 1}: Slot duration (${session.slotDuration} mins) exceeds session duration (${totalDuration} mins).`,
          );
        }
      });
    }

    // CHECK FOR OVERLAPPING SESSIONS
    for (const ovr of overrideSchedules) {
      const sortedSessions = ovr.sessions.slice().sort((a, b) => {
        return moment(a.startTime, 'HH:mm').diff(moment(b.startTime, 'HH:mm'));
      });

      for (let i = 0; i < sortedSessions.length - 1; i++) {
        const currentEnd = moment(sortedSessions[i].endTime, 'HH:mm');
        const nextStart = moment(sortedSessions[i + 1].startTime, 'HH:mm');

        if (currentEnd.isAfter(nextStart)) {
          throw new BadRequestException(
            `Session ${i + 1} overlaps with session ${i + 2}.`,
          );
        }
      }
    }

    // UPDATE OVERRIDE SCHEDULES
    for (const ovr of overrideSchedules) {
      const existingOverride = await this.overrideScheduleRepository.findOne({
        where: { doctor: { id: doctor.id }, date: ovr.date },
      });

      // UPDATE TIMEZONE IF PROVIDED
      if (ovr.timezone) {
        existingOverride.timezone = ovr.timezone;
        await this.overrideScheduleRepository.save(existingOverride);
      }

      // CLEAR OLD SESSIONS
      await this.overrideSessionRepository.delete({
        overrideSchedule: { id: existingOverride.id },
      });

      // CREATE NEW SESSIONS
      const newSessions = ovr.sessions.map((session) =>
        this.overrideSessionRepository.create({
          overrideSchedule: existingOverride,
          startTime: session.startTime,
          endTime: session.endTime,
          slotDuration: session.slotDuration,
        }),
      );
      await this.overrideSessionRepository.save(newSessions);
    }

    // RETURN OVERRIDE SCHEDULES WITH SESSIONS
    return this.overrideScheduleRepository.find({
      where: { doctor: { id: doctor.id } },
      relations: ['sessions'],
    });
  }
  async getDoctorSlotDetails(
    user: DoctorWithUserType | PatientWithUserType,
    doctorId: string,
    query: GetSlotDetailsDto,
  ): Promise<any> {
    const { startDate, endDate } = query;

    // SELF CHECK
    let doctorIdToUse: string;
    if (!doctorId) {
      if (user.userType !== UserType.DOCTOR) {
        throw new BadRequestException(
          'Only a doctor can fetch their own slot details if doctorId is not provided.',
        );
      }
      doctorIdToUse = user.id;
    } else {
      doctorIdToUse = doctorId;
    }

    // DOCTOR CHECK
    const doctor = await this.doctorService.findById(doctorIdToUse);

    if (!doctor) {
      throw new BadRequestException('No doctor found for this id');
    }

    if (!startDate || !endDate) {
      throw new BadRequestException(
        'Please provide both startDate and endDate in the query params',
      );
    }

    // CONVERT TO DATE
    const start = moment(startDate, 'YYYY-MM-DD');
    const end = moment(endDate, 'YYYY-MM-DD');

    if (!start.isValid() || !end.isValid()) {
      throw new BadRequestException(
        'Invalid date format. Use YYYY-MM-DD for startDate and endDate.',
      );
    }

    // DATE VALIDITY CHECK
    if (start.isBefore(moment().startOf('day'))) {
      throw new BadRequestException('startDate cant be in past');
    }
    if (end.isBefore(start)) {
      throw new BadRequestException('endDate cannot be before startDate');
    }

    // CONVERT TO JS DATE FOR QUERYING
    const startDateObj = start.toDate();
    const endDateObj = end.toDate();

    // FETCH OVERRIDE SCHEDULES
    const overrideSchedules = await this.overrideScheduleRepository.find({
      where: {
        doctor: { id: doctor.id },
        date: Between(startDateObj, endDateObj),
      },
      relations: ['sessions'],
    });

    // FETCH WEEKLY SCHEDULES
    const weeklySchedules = await this.weeklyScheduleRepository.find({
      where: { doctor: { id: doctor.id } },
      relations: ['sessions'],
    });

    // NO NEED TO FETCH APPOINTMENTS UPFRONT - WILL USE isSlotBooked FOR EACH SLOT

    // CREATE MAP OF OVERRIDE-SHCEDULES BY DATE
    const overrideMap: Record<string, OverrideSchedule> = {};
    overrideSchedules.forEach((os) => {
      const dateKey = moment(os.date).format('YYYY-MM-DD');
      overrideMap[dateKey] = os;
    });

    // CREATE MAP OF WEEKLY-SHCEDULES BY DAY OF WEEK
    const weeklyMap: Record<string, WeeklySchedule> = {};
    weeklySchedules.forEach((ws) => {
      weeklyMap[ws.dayOfWeek] = ws;
    });

    // BUILD SLOT DETAILS FOR DATE RANGE
    const result = [];
    let current = moment(start);
    while (current.isSameOrBefore(end)) {
      const currentDate = current.format('YYYY-MM-DD');
      const dayName = current.format('dddd').toLowerCase() as DayOfWeek;

      // CHECK OVERRIDE FIRST
      if (overrideMap[currentDate]) {
        const override = overrideMap[currentDate];
        // MAKING SLOTS FOR EACH SESSION ACCORDING TO SLOT DURATION
        for (const s of override.sessions as (OverrideSession & { slots })[]) {
          let calculatedSessions = [];
          let current = moment(s.startTime, 'HH:mm:ss');
          const end = moment(s.endTime, 'HH:mm:ss');
          while (current.isBefore(end)) {
            const slotStartTime = current.format('HH:mm:ss');
            const slotEndTime = current
              .clone()
              .add(s.slotDuration, 'minutes')
              .format('HH:mm:ss');

            // GET BOOKING INFORMATION FOR THIS SLOT
            const slotStart = moment(
              `${currentDate} ${slotStartTime}`,
              'YYYY-MM-DD HH:mm:ss',
            ).toDate();
            const slotEnd = moment(
              `${currentDate} ${slotEndTime}`,
              'YYYY-MM-DD HH:mm:ss',
            ).toDate();

            const appointmentDetails =
              await this.appointmentService.isSlotBooked(
                doctor.id,
                slotStart,
                slotEnd,
              );

            // ONLY SHOW APPOINTMENT DETAILS IF DOCTOR IS FETCHING THEIR OWN SLOTS
            const finalAppointmentDetails =
              user.userType === UserType.DOCTOR && user.id === doctor.id
                ? appointmentDetails
                : null;

            calculatedSessions.push({
              startTime: slotStartTime,
              endTime: slotEndTime,
              isBooked: appointmentDetails !== null,
              appointmentDetails: finalAppointmentDetails,
              doctorTimezone: override.timezone, // Include doctor's timezone
            });
            current.add(s.slotDuration, 'minutes');
          }
          s.slots = calculatedSessions;
        }

        // COMIBINE ALL SLOTS
        let allSlotsForThisDate = [];
        override.sessions.forEach(
          (
            s: OverrideSession & {
              slots;
            },
          ) => {
            allSlotsForThisDate.push(...s.slots);
          },
        );

        result.push({
          date: currentDate,
          isOverride: true,
          slots: allSlotsForThisDate,
        });
      } else {
        // CHECK WEEKLY
        if (weeklyMap[dayName]) {
          const weekly = weeklyMap[dayName];
          // MAKING SLOTS FOR EACH SESSION ACCORDING TO SLOT DURATION
          for (const s of weekly.sessions as (WeeklySession & { slots })[]) {
            let calculatedSessions = [];
            let current = moment(s.startTime, 'HH:mm:ss');
            const end = moment(s.endTime, 'HH:mm:ss');
            while (current.isBefore(end)) {
              const slotStartTime = current.format('HH:mm:ss');
              const slotEndTime = current
                .clone()
                .add(s.slotDuration, 'minutes')
                .format('HH:mm:ss');

              // GET BOOKING INFORMATION FOR THIS SLOT
              const slotStart = moment(
                `${currentDate} ${slotStartTime}`,
                'YYYY-MM-DD HH:mm:ss',
              ).toDate();
              const slotEnd = moment(
                `${currentDate} ${slotEndTime}`,
                'YYYY-MM-DD HH:mm:ss',
              ).toDate();

              const appointmentDetails =
                await this.appointmentService.isSlotBooked(
                  doctor.id,
                  slotStart,
                  slotEnd,
                );

              // ONLY SHOW APPOINTMENT DETAILS IF DOCTOR IS FETCHING THEIR OWN SLOTS
              const finalAppointmentDetails =
                user.userType === UserType.DOCTOR && user.id === doctor.id
                  ? appointmentDetails
                  : null;

              calculatedSessions.push({
                startTime: slotStartTime,
                endTime: slotEndTime,
                isBooked: appointmentDetails !== null,
                appointmentDetails: finalAppointmentDetails,
                doctorTimezone: weekly.timezone, // Include doctor's timezone
              });
              current.add(s.slotDuration, 'minutes');
            }
            s.slots = calculatedSessions;
          }

          // COMIBINE ALL SLOTS
          let allSlotsForThisDay = [];
          weekly.sessions.forEach(
            (
              s: WeeklySession & {
                slots;
              },
            ) => {
              allSlotsForThisDay.push(...s.slots);
            },
          );
          result.push({
            date: currentDate,
            isOverride: false,
            slots: allSlotsForThisDay,
          });
        } else {
          // NO SCHEDULE
          result.push({
            date: currentDate,
            isOverride: false,
            sessions: [],
          });
        }
      }

      current.add(1, 'day');
    }

    return result;
  }
  async getWeeklyScheduleOfDoctor(
    user: DoctorWithUserType | PatientWithUserType,
    doctorId: string,
  ): Promise<any> {
    // SELF CHECK
    let doctorIdToUse: string;
    if (!doctorId) {
      if (user.userType !== UserType.DOCTOR) {
        throw new BadRequestException(
          'Only a doctor can fetch their own slot details if doctorId is not provided.',
        );
      }
      doctorIdToUse = user.id;
    } else {
      doctorIdToUse = doctorId;
    }

    // DOCTOR CHECK
    const doctor = await this.doctorService.findById(doctorIdToUse);

    if (!doctor) {
      throw new BadRequestException('No doctor found for this id');
    }

    const savedWeeklySchedules = this.weeklyScheduleRepository.find({
      where: { doctor: { id: doctor.id } },
      relations: ['sessions'],
    });

    return savedWeeklySchedules;
  }
  async getOverrideScheduleOfDoctor(
    user: DoctorWithUserType | PatientWithUserType,
    doctorId: string,
  ): Promise<any> {
    // SELF CHECK
    let doctorIdToUse: string;
    if (!doctorId) {
      if (user.userType !== UserType.DOCTOR) {
        throw new BadRequestException(
          'Only a doctor can fetch their own slot details if doctorId is not provided.',
        );
      }
      doctorIdToUse = user.id;
    } else {
      doctorIdToUse = doctorId;
    }

    // DOCTOR CHECK
    const doctor = await this.doctorService.findById(doctorIdToUse);

    if (!doctor) {
      throw new BadRequestException('No doctor found for this id');
    }

    const savedOverrideSchedules = this.overrideScheduleRepository.find({
      where: { doctor: { id: doctor.id } },
      relations: ['sessions'],
    });

    return savedOverrideSchedules;
  }

  // HELPER FUNCTIONS
  // GETS ALL DOCTORS AVAILABLE ON A SPECIFIC DATE
  async getDoctorsAccordingToDate(date: string): Promise<Doctor[]> {
    // DATE VALIDITY CHECK
    if (!date) {
      throw new BadRequestException('Date is required');
    }
    const requestedDate = moment(date, 'YYYY-MM-DD', true);
    if (!requestedDate.isValid()) {
      throw new BadRequestException('Invalid date format. Use YYYY-MM-DD');
    }

    // DATE IN FUTURE CHECK
    if (requestedDate.isBefore(moment().startOf('day'))) {
      throw new BadRequestException('Date cannot be in the past.');
    }
    const dayOfWeek = requestedDate.format('dddd').toLowerCase() as DayOfWeek;

    // OVERRIDE SCHEDULES FIRST
    const overrideSchedules = await this.overrideScheduleRepository.find({
      where: { date: requestedDate.toDate() },
      relations: ['doctor', 'sessions'],
    });

    const weeklySchedules = await this.weeklyScheduleRepository.find({
      where: { dayOfWeek },
      relations: ['doctor', 'sessions'],
    });

    // HELPER FUCNTION TO CHECK IF SLOT EXISTS
    const scheduleHasAtLeastOneSlot = (
      sessions: (OverrideSession | WeeklySession)[],
    ): boolean => {
      for (const s of sessions) {
        const start = moment(s.startTime, 'HH:mm:ss');
        const end = moment(s.endTime, 'HH:mm:ss');
        const slotDuration = s.slotDuration;

        if (!start.isValid() || !end.isValid() || !slotDuration) {
          continue;
        }

        // IF START IS BEFORE END THEN ONE SLOT EXISTS
        if (start.isBefore(end)) {
          return true;
        }
      }
      return false;
    };

    const overrideDoctorsWithSlots = new Set<Doctor>();
    overrideSchedules.forEach((os) => {
      if (scheduleHasAtLeastOneSlot(os.sessions)) {
        overrideDoctorsWithSlots.add(os.doctor);
      }
    });

    const weeklyDoctorsWithSlots = new Set<Doctor>();
    weeklySchedules.forEach((ws) => {
      const docAlreadyHasOverride = [...overrideDoctorsWithSlots].some(
        (doc) => doc.id === ws.doctor.id,
      );
      if (docAlreadyHasOverride) return;

      if (scheduleHasAtLeastOneSlot(ws.sessions)) {
        weeklyDoctorsWithSlots.add(ws.doctor);
      }
    });

    const allAvailableDoctors = new Set<Doctor>([
      ...overrideDoctorsWithSlots,
      ...weeklyDoctorsWithSlots,
    ]);

    return Array.from(allAvailableDoctors);
  }

  // VALIDATES IF APPOINTMENT TIME MATCHES A VALID DOCTOR SLOT
  async validateAppointmentSlot(
    doctorId: string,
    startDateTime: Date,
    endDateTime: Date,
  ): Promise<boolean> {
    const appointmentDate = moment(startDateTime).format('YYYY-MM-DD');
    const appointmentStartTime = moment(startDateTime).format('HH:mm:ss');
    const appointmentEndTime = moment(endDateTime).format('HH:mm:ss');
    console.log('APPOINTMENT START TIME', appointmentStartTime);
    console.log('APPOINTMENT END TIME', appointmentEndTime);
    const dayOfWeek = moment(startDateTime)
      .format('dddd')
      .toLowerCase() as DayOfWeek;

    console.log('DAY OF WEEK', dayOfWeek);

    // FETCH OVERRIDE SCHEDULE FOR THE DATE
    const overrideSchedule = await this.overrideScheduleRepository.findOne({
      where: {
        doctor: { id: doctorId },
        date: moment(appointmentDate).toDate(),
      },
      relations: ['sessions'],
    });

    console.log('OVERRIDE SCHEDULE', overrideSchedule);

    // CHECK OVERRIDE SCHEDULE FIRST
    if (overrideSchedule && overrideSchedule.sessions.length > 0) {
      return this.checkSlotInSessions(
        overrideSchedule.sessions,
        appointmentStartTime,
        appointmentEndTime,
      );
    }

    // FETCH WEEKLY SCHEDULE FOR THE DAY
    const weeklySchedule = await this.weeklyScheduleRepository.findOne({
      where: {
        doctor: { id: doctorId },
        dayOfWeek: dayOfWeek,
      },
      relations: ['sessions'],
    });

    console.log('WEEKLY SCHEDULE', weeklySchedule);

    if (weeklySchedule && weeklySchedule.sessions.length > 0) {
      return this.checkSlotInSessions(
        weeklySchedule.sessions,
        appointmentStartTime,
        appointmentEndTime,
      );
    }

    // NO SCHEDULE FOUND
    return false;
  }

  // CHECKS IF APPOINTMENT TIME MATCHES ANY SLOT IN SESSIONS
  private checkSlotInSessions(
    sessions: (OverrideSession | WeeklySession)[],
    appointmentStartTime: string,
    appointmentEndTime: string,
  ): boolean {
    for (const session of sessions) {
      let current = moment(session.startTime, 'HH:mm:ss');
      const end = moment(session.endTime, 'HH:mm:ss');
      console.log('SESSION START TIME', current);
      console.log('SESSION END TIME', end);
      while (current.isBefore(end)) {
        const slotStartTime = current.format('HH:mm:ss');
        const slotEndTime = current
          .clone()
          .add(session.slotDuration, 'minutes')
          .format('HH:mm:ss');
        console.log('SLOT START TIME', slotStartTime);
        console.log('SLOT END TIME', slotEndTime);
        // CHECK IF APPOINTMENT TIMES EXACTLY MATCH THIS SLOT
        if (
          appointmentStartTime === slotStartTime &&
          appointmentEndTime === slotEndTime
        ) {
          return true;
        }

        current.add(session.slotDuration, 'minutes');
      }
    }

    return false;
  }
}
